# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.5](https://github.com/es-shims/TypedArray.prototype.slice/compare/v1.0.4...v1.0.5) - 2025-01-02

### Commits

- [Fix] (－‸ლ) typo in specifier from dd67051195bdc0a9a5a2252c0fd3bd9155f6b08b [`cffa05e`](https://github.com/es-shims/TypedArray.prototype.slice/commit/cffa05e88cb2c3044f367ba9541f2894f51c99d0)

## [v1.0.4](https://github.com/es-shims/TypedArray.prototype.slice/compare/v1.0.3...v1.0.4) - 2025-01-02

### Commits

- [actions] split out node 10-20, and 20+ [`1ca7eb2`](https://github.com/es-shims/TypedArray.prototype.slice/commit/1ca7eb25ea741498bc6aff9df2d7a439e7cc374e)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`259de62`](https://github.com/es-shims/TypedArray.prototype.slice/commit/259de620bf2fa073e3c33492d0928cdf0a1a693e)
- [Deps] update `call-bind`, `es-abstract`, `typed-array-buffer`, `typed-array-byte-offset` [`5c9dbf9`](https://github.com/es-shims/TypedArray.prototype.slice/commit/5c9dbf99d524c02d0f67a7ceff1a7f9586c28311)
- [Refactor] use `math-intrinsics` directly [`dd67051`](https://github.com/es-shims/TypedArray.prototype.slice/commit/dd67051195bdc0a9a5a2252c0fd3bd9155f6b08b)
- [Refactor] use `get-proto` and `math-intrinsics` directly [`20395cc`](https://github.com/es-shims/TypedArray.prototype.slice/commit/20395cc9425ef1794023b69916a0702c6d94f4e4)
- [Tests] replace `aud` with `npm audit` [`53d2fef`](https://github.com/es-shims/TypedArray.prototype.slice/commit/53d2fefed9c793e51d265aa56360be042b405f3b)
- [Dev Deps] add missing peer dep [`70a2f57`](https://github.com/es-shims/TypedArray.prototype.slice/commit/70a2f57f29091ffdc14fd43e03520151a825e2b3)

## [v1.0.3](https://github.com/es-shims/TypedArray.prototype.slice/compare/v1.0.2...v1.0.3) - 2024-03-15

### Commits

- [Deps] update `es-abstract` [`9067824`](https://github.com/es-shims/TypedArray.prototype.slice/commit/906782435b34ec0ce54a9dd613974601e08b5c2e)
- [Deps] update `call-bind`, `typed-array-buffer`, `typed-array-byte-offset` [`33da086`](https://github.com/es-shims/TypedArray.prototype.slice/commit/33da086e39deedf551abd8398fdf5d189cbe3d77)
- [Dev Deps] update `available-typed-arrays`, `tape` [`2f8d038`](https://github.com/es-shims/TypedArray.prototype.slice/commit/2f8d038eb15b8ac3206c2368036b988b01e5c417)

## [v1.0.2](https://github.com/es-shims/TypedArray.prototype.slice/compare/v1.0.1...v1.0.2) - 2024-02-06

### Commits

- [Dev Deps] update `aud`, `anvailable-typed-arrays`, `npmignore`, `object-inspect`, `tape` [`9334d4e`](https://github.com/es-shims/TypedArray.prototype.slice/commit/9334d4e47106747bdf08072c362352b189fa7778)
- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `get-intrinsic` [`0acd649`](https://github.com/es-shims/TypedArray.prototype.slice/commit/0acd649f03baf849a88e27cd0dcfd78c400a5cb3)
- [Refactor] use `es-errors`, so things that only need those do not need `get-intrinsic` [`70215ad`](https://github.com/es-shims/TypedArray.prototype.slice/commit/70215adca0d4b948a9945cc7d5c0a7a70fbac2a8)

## [v1.0.1](https://github.com/es-shims/TypedArray.prototype.slice/compare/v1.0.0...v1.0.1) - 2023-07-18

### Commits

- [readme] fix title and URLs [`391dc87`](https://github.com/es-shims/TypedArray.prototype.slice/commit/391dc876acb64d7780fded8be0d65d5f0caab688)

## v1.0.0 - 2023-07-18

### Commits

- Initial implementation, tests, readme [`7684f32`](https://github.com/es-shims/TypedArray.prototype.slice/commit/7684f32983b444734c6293af807fcf5f3e20a9ad)
- Initial commit [`e4ff962`](https://github.com/es-shims/TypedArray.prototype.slice/commit/e4ff962b319dbc568f48e312e0396130e91d5df4)
- [Fix] node v0.11.4 - 3 have an own slice method that works incorrectly [`361b2c6`](https://github.com/es-shims/TypedArray.prototype.slice/commit/361b2c6046f756fb6f6851b0d8759b5e88324521)
- [Fix] `node &lt; v0.6` lacks proper toString behavior on Typed Arrays [`bcf30f9`](https://github.com/es-shims/TypedArray.prototype.slice/commit/bcf30f916c59d2744f5a0c2e8a9ea38da2092413)
- npm init [`a24ef46`](https://github.com/es-shims/TypedArray.prototype.slice/commit/a24ef465139926277e5b85b8d9b17e96070aa6c8)
- [Fix] update `es-abstract` and move it to runtime deps [`b70eb79`](https://github.com/es-shims/TypedArray.prototype.slice/commit/b70eb7926485148733d56bec6c9ce91ac820bd0d)
- Only apps should have lockfiles [`3361cbe`](https://github.com/es-shims/TypedArray.prototype.slice/commit/3361cbe1c5a986a194f250360cfd4a52a2e255a5)
