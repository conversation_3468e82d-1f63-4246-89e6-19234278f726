{"name": "through2", "version": "2.0.5", "description": "A tiny wrapper around Node streams2 Transform to avoid explicit subclassing noise", "main": "through2.js", "scripts": {"test": "node test/test.js | faucet"}, "repository": {"type": "git", "url": "https://github.com/rvagg/through2.git"}, "keywords": ["stream", "streams2", "through", "transform"], "author": "<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}, "devDependencies": {"bl": "~2.0.1", "faucet": "0.0.1", "nyc": "~13.1.0", "safe-buffer": "~5.1.2", "stream-spigot": "~3.0.6", "tape": "~4.9.1"}}