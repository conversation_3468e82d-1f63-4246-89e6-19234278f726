{"name": "type-fest", "version": "0.12.0", "description": "A collection of essential TypeScript types", "license": "(MIT OR CC0-1.0)", "repository": "sindresorhus/type-fest", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsd"}, "files": ["index.d.ts", "source"], "keywords": ["typescript", "ts", "types", "utility", "util", "utilities", "omit", "merge", "json"], "devDependencies": {"@sindresorhus/tsconfig": "^0.7.0", "@typescript-eslint/eslint-plugin": "^2.22.0", "@typescript-eslint/parser": "^2.22.0", "eslint-config-xo-typescript": "^0.26.0", "tsd": "^0.7.3", "typescript": "^3.8.3", "xo": "^0.27.2"}, "types": "index.d.ts", "xo": {"rules": {"@typescript-eslint/indent": "off"}}}