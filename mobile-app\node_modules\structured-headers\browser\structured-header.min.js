var structuredHeader;!function(){"use strict";var e={590:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.Token=void 0,s(r(223),t),s(r(759),t),s(r(985),t);var n=r(0);Object.defineProperty(t,"Token",{enumerable:!0,get:function(){return n.Token}})},759:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.ParseError=t.parseItem=t.parseList=t.parseDictionary=void 0;const i=r(985),s=r(0),n=r(128);t.parseDictionary=function(e){return new a(e).parseDictionary()},t.parseList=function(e){return new a(e).parseList()},t.parseItem=function(e){return new a(e).parseItem()};class o extends Error{constructor(e,t){super(`Parse error: ${t} at offset ${e}`)}}t.ParseError=o;class a{constructor(e){this.input=e,this.pos=0}parseDictionary(){this.skipWS();const e=new Map;for(;!this.eof();){const t=this.parseKey();let r;if("="===this.lookChar()?(this.pos++,r=this.parseItemOrInnerList()):r=[!0,this.parseParameters()],e.set(t,r),this.skipOWS(),this.eof())return e;if(this.expectChar(","),this.pos++,this.skipOWS(),this.eof())throw new o(this.pos,"Dictionary contained a trailing comma")}return e}parseList(){this.skipWS();const e=[];for(;!this.eof();){if(e.push(this.parseItemOrInnerList()),this.skipOWS(),this.eof())return e;if(this.expectChar(","),this.pos++,this.skipOWS(),this.eof())throw new o(this.pos,"A list may not end with a trailing comma")}return e}parseItem(e=!0){e&&this.skipWS();const t=[this.parseBareItem(),this.parseParameters()];return e&&this.checkTrail(),t}parseItemOrInnerList(){return"("===this.lookChar()?this.parseInnerList():this.parseItem(!1)}parseInnerList(){this.expectChar("("),this.pos++;const e=[];for(;!this.eof();){if(this.skipWS(),")"===this.lookChar())return this.pos++,[e,this.parseParameters()];e.push(this.parseItem(!1));const t=this.lookChar();if(" "!==t&&")"!==t)throw new o(this.pos,"Expected a whitespace or ) after every item in an inner list")}throw new o(this.pos,"Could not find end of inner list")}parseBareItem(){const e=this.lookChar();if(e.match(/^[-0-9]/))return this.parseIntegerOrDecimal();if('"'===e)return this.parseString();if(e.match(/^[A-Za-z*]/))return this.parseToken();if(":"===e)return this.parseByteSequence();if("?"===e)return this.parseBoolean();throw new o(this.pos,"Unexpected input")}parseParameters(){const e=new Map;for(;!this.eof()&&";"===this.lookChar();){this.pos++,this.skipWS();const t=this.parseKey();let r=!0;"="===this.lookChar()&&(this.pos++,r=this.parseBareItem()),e.set(t,r)}return e}parseIntegerOrDecimal(){let e="integer",t=1,r="";if("-"===this.lookChar()&&(t=-1,this.pos++),!c(this.lookChar()))throw new o(this.pos,"Expected a digit (0-9)");for(;!this.eof();){const t=this.getChar();if(c(t))r+=t;else{if("integer"!==e||"."!==t){this.pos--;break}if(r.length>12)throw new o(this.pos,"Exceeded maximum decimal length");r+=".",e="decimal"}if("integer"===e&&r.length>15)throw new o(this.pos,"Exceeded maximum integer length");if("decimal"===e&&r.length>16)throw new o(this.pos,"Exceeded maximum decimal length")}if("integer"===e)return parseInt(r,10)*t;if(r.endsWith("."))throw new o(this.pos,"Decimal cannot end on a period");if(r.split(".")[1].length>3)throw new o(this.pos,"Number of digits after the decimal point cannot exceed 3");return parseFloat(r)*t}parseString(){let e="";for(this.expectChar('"'),this.pos++;!this.eof();){const t=this.getChar();if("\\"===t){if(this.eof())throw new o(this.pos,"Unexpected end of input");const t=this.getChar();if("\\"!==t&&'"'!==t)throw new o(this.pos,"A backslash must be followed by another backslash or double quote");e+=t}else{if('"'===t)return e;if(!n.isAscii(t))throw new Error("Strings must be in the ASCII range");e+=t}}throw new o(this.pos,"Unexpected end of input")}parseToken(){let e="";for(;!this.eof();){const t=this.lookChar();if(!/^[:/!#$%&'*+\-.^_`|~A-Za-z0-9]$/.test(t))return new s.Token(e);e+=this.getChar()}return new s.Token(e)}parseByteSequence(){this.expectChar(":"),this.pos++;const e=this.input.indexOf(":",this.pos);if(-1===e)throw new o(this.pos,'Could not find a closing ":" character to mark end of Byte Sequence');const t=this.input.substring(this.pos,e);if(this.pos+=t.length+1,!/^[A-Za-z0-9+/=]*$/.test(t))throw new o(this.pos,"ByteSequence does not contain a valid base64 string");return new i.ByteSequence(t)}parseBoolean(){this.expectChar("?"),this.pos++;const e=this.getChar();if("1"===e)return!0;if("0"===e)return!1;throw new o(this.pos,'Unexpected character. Expected a "1" or a "0"')}parseKey(){if(!this.lookChar().match(/^[a-z*]/))throw new o(this.pos,"A key must begin with an asterisk or letter (a-z)");let e="";for(;!this.eof();){const t=this.lookChar();if(!/^[a-z0-9_\-.*]$/.test(t))return e;e+=this.getChar()}return e}lookChar(){return this.input[this.pos]}expectChar(e){if(this.lookChar()!==e)throw new o(this.pos,`Expected ${e}`)}getChar(){return this.input[this.pos++]}eof(){return this.pos>=this.input.length}skipOWS(){for(;;){const e=this.input.substr(this.pos,1);if(" "!==e&&"\t"!==e)break;this.pos++}}skipWS(){for(;" "===this.lookChar();)this.pos++}checkTrail(){if(this.skipWS(),!this.eof())throw new o(this.pos,"Unexpected characters at end of input")}}t.default=a;const h=/^[0-9]$/;function c(e){return h.test(e)}},223:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.serializeItem=t.serializeDictionary=t.serializeList=t.SerializeError=void 0;const i=r(985),s=r(0),n=r(128);class o extends Error{}function a(e){return c(e[0])+u(e[1])}function h(e){return`(${e[0].map((e=>a(e))).join(" ")})${u(e[1])}`}function c(e){if("number"==typeof e)return Number.isInteger(e)?function(e){if(e<-999999999999999||e>999999999999999)throw new o("Structured headers can only encode integers in the range range of -999,999,999,999,999 to 999,999,999,999,999 inclusive");return e.toString()}(e):function(e){const t=e.toFixed(3).replace(/0+$/,"");if(t.split(".")[0].replace("-","").length>12)throw new o("Fractional numbers are not allowed to have more than 12 significant digits before the decimal point");return t}(e);if("string"==typeof e)return function(e){if(!n.isAscii(e))throw new o("Only ASCII strings may be serialized");return`"${e.replace(/("|\\)/g,(e=>"\\"+e))}"`}(e);if(e instanceof s.Token)return function(e){return e.toString()}(e);if(e instanceof i.ByteSequence)return function(e){return`:${e.toBase64()}:`}(e);if("boolean"==typeof e)return function(e){return e?"?1":"?0"}(e);throw new o("Cannot serialize values of type "+typeof e)}function u(e){return Array.from(e).map((([e,t])=>{let r=";"+p(e);return!0!==t&&(r+="="+c(t)),r})).join("")}function p(e){if(!n.isValidKeyStr(e))throw new o("Keys in dictionaries must only contain lowercase letter, numbers, _-*. and must start with a letter or *");return e}t.SerializeError=o,t.serializeList=function(e){return e.map((e=>n.isInnerList(e)?h(e):a(e))).join(", ")},t.serializeDictionary=function(e){return Array.from(e.entries()).map((([e,t])=>{let r=p(e);return!0===t[0]?r+=u(t[1]):(r+="=",n.isInnerList(t)?r+=h(t):r+=a(t)),r})).join(", ")},t.serializeItem=a},0:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),t.Token=void 0;const i=r(128);t.Token=class{constructor(e){if(!i.isValidTokenStr(e))throw new TypeError("Invalid character in Token string. Tokens must start with *, A-Z and the rest of the string may only contain a-z, A-Z, 0-9, :/!#$%&'*+-.^_`|~");this.value=e}toString(){return this.value}}},985:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ByteSequence=void 0,t.ByteSequence=class{constructor(e){this.base64Value=e}toBase64(){return this.base64Value}}},128:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.isInnerList=t.isValidKeyStr=t.isValidTokenStr=t.isAscii=void 0;const r=/^[\x20-\x7E]*$/,i=/^[a-zA-Z*][:/!#$%&'*+\-.^_`|~A-Za-z0-9]*$/,s=/^[a-z*][*\-_.a-z0-9]*$/;t.isAscii=function(e){return r.test(e)},t.isValidTokenStr=function(e){return i.test(e)},t.isValidKeyStr=function(e){return s.test(e)},t.isInnerList=function(e){return Array.isArray(e[0])}}},t={},r=function r(i){var s=t[i];if(void 0!==s)return s.exports;var n=t[i]={exports:{}};return e[i].call(n.exports,n,n.exports,r),n.exports}(590);structuredHeader=r}();
//# sourceMappingURL=structured-header.min.js.map